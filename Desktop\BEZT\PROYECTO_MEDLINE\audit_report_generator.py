# Archivo: audit_report_generator.py

import os
import pandas as pd
from datetime import datetime
from openpyxl import Workbook
from openpyxl.styles import PatternFill, Font, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.utils import get_column_letter
from typing import Dict, Any, List

from incident_reporter import incident_reporter, IncidentSeverity
from consistency_validator import consistency_validator
from overdischarge_controller import overdischarge_controller

class AuditReportGenerator:
    """Generador de reportes de auditoría para el sistema de drawback"""
    
    def __init__(self):
        self.styles = self._create_styles()
    
    def _create_styles(self) -> Dict[str, Dict[str, Any]]:
        """Crea los estilos para el formato de Excel"""
        return {
            'header_critical': {
                'fill': PatternFill(start_color='FF6B6B', end_color='FF6B6B', fill_type='solid'),
                'font': Font(bold=True, color='FFFFFF'),
                'alignment': Alignment(horizontal='center', vertical='center')
            },
            'header_warning': {
                'fill': PatternFill(start_color='FFE66D', end_color='FFE66D', fill_type='solid'),
                'font': Font(bold=True, color='000000'),
                'alignment': Alignment(horizontal='center', vertical='center')
            },
            'header_success': {
                'fill': PatternFill(start_color='4ECDC4', end_color='4ECDC4', fill_type='solid'),
                'font': Font(bold=True, color='FFFFFF'),
                'alignment': Alignment(horizontal='center', vertical='center')
            },
            'header_info': {
                'fill': PatternFill(start_color='95E1D3', end_color='95E1D3', fill_type='solid'),
                'font': Font(bold=True, color='000000'),
                'alignment': Alignment(horizontal='center', vertical='center')
            },
            'cell_critical': {
                'fill': PatternFill(start_color='FFEBEE', end_color='FFEBEE', fill_type='solid'),
                'font': Font(color='D32F2F')
            },
            'cell_warning': {
                'fill': PatternFill(start_color='FFF8E1', end_color='FFF8E1', fill_type='solid'),
                'font': Font(color='F57C00')
            },
            'cell_success': {
                'fill': PatternFill(start_color='E8F5E8', end_color='E8F5E8', fill_type='solid'),
                'font': Font(color='2E7D32')
            }
        }
    
    def generate_incident_report(self, output_folder: str, log_callback=None) -> str:
        """
        Genera el reporte de incidencias en formato Excel
        
        Returns:
            Ruta del archivo generado
        """
        if log_callback:
            log_callback("Generando reporte de incidencias...")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"REPORTE_INCIDENCIAS_{timestamp}.xlsx"
        filepath = os.path.join(output_folder, filename)
        
        wb = Workbook()
        
        # Eliminar hoja por defecto
        wb.remove(wb.active)
        
        # 1. Hoja de Resumen Ejecutivo
        self._create_executive_summary_sheet(wb)
        
        # 2. Hoja de Errores Críticos
        self._create_critical_errors_sheet(wb)
        
        # 3. Hoja de Advertencias
        self._create_warnings_sheet(wb)
        
        # 4. Hoja de Resumen por Categoría
        self._create_category_summary_sheet(wb)
        
        # 5. Hoja de Estadísticas Detalladas
        self._create_detailed_statistics_sheet(wb)
        
        # Guardar archivo
        wb.save(filepath)
        
        if log_callback:
            log_callback(f"✅ Reporte de incidencias generado: {filename}")
        
        return filepath
    
    def generate_audit_report(self, output_folder: str, log_callback=None) -> str:
        """
        Genera el reporte de auditoría completo en formato Excel
        
        Returns:
            Ruta del archivo generado
        """
        if log_callback:
            log_callback("Generando reporte de auditoría...")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"REPORTE_AUDITORIA_{timestamp}.xlsx"
        filepath = os.path.join(output_folder, filename)
        
        wb = Workbook()
        
        # Eliminar hoja por defecto
        wb.remove(wb.active)
        
        # 1. Hoja de Resumen Ejecutivo
        self._create_audit_executive_summary_sheet(wb)
        
        # 2. Hoja de Validaciones de Consistencia
        self._create_consistency_validation_sheet(wb)
        
        # 3. Hoja de Control de Sobredescargo
        self._create_overdischarge_control_sheet(wb)
        
        # 4. Hoja de Detalles de Sobredescargo
        self._create_overdischarge_details_sheet(wb)
        
        # 5. Hoja de Resumen por Partida
        self._create_partida_summary_sheet(wb)
        
        # Guardar archivo
        wb.save(filepath)
        
        if log_callback:
            log_callback(f"✅ Reporte de auditoría generado: {filename}")
        
        return filepath
    
    def _create_executive_summary_sheet(self, wb: Workbook):
        """Crea la hoja de resumen ejecutivo para incidencias"""
        ws = wb.create_sheet("Resumen Ejecutivo")
        
        # Título
        ws['A1'] = "REPORTE DE INCIDENCIAS - RESUMEN EJECUTIVO"
        ws['A1'].font = Font(size=16, bold=True)
        ws.merge_cells('A1:F1')
        
        # Fecha y hora
        ws['A3'] = f"Generado: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        # Estadísticas generales
        stats = incident_reporter.get_summary_stats()
        
        row = 5
        ws[f'A{row}'] = "ESTADÍSTICAS GENERALES"
        ws[f'A{row}'].font = Font(bold=True)
        row += 1
        
        summary_data = [
            ["Total de registros procesados", stats['total_records']],
            ["Registros exitosos", stats['successful_records']],
            ["Registros con errores", stats['failed_records']],
            ["Total de incidencias", stats['total_incidents']],
            ["Incidencias críticas", stats['critical_incidents']],
            ["Errores", stats['error_incidents']],
            ["Advertencias", stats['warning_incidents']],
            ["Tasa de éxito", f"{stats['success_rate']:.1f}%"]
        ]
        
        for item, value in summary_data:
            ws[f'A{row}'] = item
            ws[f'B{row}'] = value
            
            # Aplicar color según el tipo
            if "críticas" in item or "errores" in item:
                ws[f'B{row}'].fill = self.styles['cell_critical']['fill']
            elif "Advertencias" in item:
                ws[f'B{row}'].fill = self.styles['cell_warning']['fill']
            elif "exitosos" in item or "éxito" in item:
                ws[f'B{row}'].fill = self.styles['cell_success']['fill']
            
            row += 1
        
        # Ajustar ancho de columnas
        ws.column_dimensions['A'].width = 30
        ws.column_dimensions['B'].width = 15
    
    def _create_critical_errors_sheet(self, wb: Workbook):
        """Crea la hoja de errores críticos"""
        ws = wb.create_sheet("Errores Críticos")
        
        # Obtener errores críticos
        critical_incidents = incident_reporter.get_incidents_by_severity(
            IncidentSeverity.CRITICO
        )
        
        if critical_incidents:
            df = pd.DataFrame([inc.to_dict() for inc in critical_incidents])
            
            # Escribir datos
            for r in dataframe_to_rows(df, index=False, header=True):
                ws.append(r)
            
            # Aplicar estilos a encabezados
            for col in range(1, len(df.columns) + 1):
                cell = ws.cell(row=1, column=col)
                cell.fill = self.styles['header_critical']['fill']
                cell.font = self.styles['header_critical']['font']
                cell.alignment = self.styles['header_critical']['alignment']
        else:
            ws['A1'] = "No se encontraron errores críticos"
            ws['A1'].fill = self.styles['cell_success']['fill']
    
    def _create_warnings_sheet(self, wb: Workbook):
        """Crea la hoja de advertencias"""
        ws = wb.create_sheet("Advertencias")
        
        # Obtener advertencias
        warning_incidents = incident_reporter.get_incidents_by_severity(
            IncidentSeverity.ADVERTENCIA
        )
        
        if warning_incidents:
            df = pd.DataFrame([inc.to_dict() for inc in warning_incidents])
            
            # Escribir datos
            for r in dataframe_to_rows(df, index=False, header=True):
                ws.append(r)
            
            # Aplicar estilos a encabezados
            for col in range(1, len(df.columns) + 1):
                cell = ws.cell(row=1, column=col)
                cell.fill = self.styles['header_warning']['fill']
                cell.font = self.styles['header_warning']['font']
                cell.alignment = self.styles['header_warning']['alignment']
        else:
            ws['A1'] = "No se encontraron advertencias"
            ws['A1'].fill = self.styles['cell_success']['fill']
    
    def _create_category_summary_sheet(self, wb: Workbook):
        """Crea la hoja de resumen por categoría"""
        ws = wb.create_sheet("Resumen por Categoría")
        
        df = incident_reporter.get_summary_by_type()
        
        if not df.empty:
            # Escribir datos
            for r in dataframe_to_rows(df, index=False, header=True):
                ws.append(r)
            
            # Aplicar estilos a encabezados
            for col in range(1, len(df.columns) + 1):
                cell = ws.cell(row=1, column=col)
                cell.fill = self.styles['header_info']['fill']
                cell.font = self.styles['header_info']['font']
                cell.alignment = self.styles['header_info']['alignment']
        else:
            ws['A1'] = "No hay datos de categorías disponibles"
    
    def _create_detailed_statistics_sheet(self, wb: Workbook):
        """Crea la hoja de estadísticas detalladas"""
        ws = wb.create_sheet("Estadísticas Detalladas")
        
        # Estadísticas de sobredescargo
        overdischarge_stats = overdischarge_controller.get_statistics()
        
        row = 1
        ws[f'A{row}'] = "ESTADÍSTICAS DE CONTROL DE SOBREDESCARGO"
        ws[f'A{row}'].font = Font(bold=True, size=14)
        row += 2
        
        overdischarge_data = [
            ["Total de partidas procesadas", overdischarge_stats['total_partidas']],
            ["Partidas normales", overdischarge_stats['partidas_normales']],
            ["Partidas con sobredescargo", overdischarge_stats['partidas_con_sobredescargo']],
            ["Porcentaje de sobredescargo", f"{overdischarge_stats['porcentaje_sobredescargo']:.1f}%"],
            ["Total cantidad descargada", f"{overdischarge_stats['total_cantidad_descargada']:.2f}"],
            ["Total límite importado", f"{overdischarge_stats['total_limite_importado']:.2f}"],
            ["Total sobredescargo", f"{overdischarge_stats['total_sobredescargo']:.2f}"],
            ["Eficiencia de descarga", f"{overdischarge_stats['eficiencia_descarga']:.1f}%"]
        ]
        
        for item, value in overdischarge_data:
            ws[f'A{row}'] = item
            ws[f'B{row}'] = value
            
            # Aplicar colores según el valor
            if "sobredescargo" in item and isinstance(value, str) and "%" in value:
                percentage = float(value.replace('%', ''))
                if percentage > 0:
                    ws[f'B{row}'].fill = self.styles['cell_critical']['fill']
                else:
                    ws[f'B{row}'].fill = self.styles['cell_success']['fill']
            
            row += 1
        
        # Ajustar ancho de columnas
        ws.column_dimensions['A'].width = 35
        ws.column_dimensions['B'].width = 20
    
    def _create_audit_executive_summary_sheet(self, wb: Workbook):
        """Crea la hoja de resumen ejecutivo para auditoría"""
        ws = wb.create_sheet("Resumen Ejecutivo")
        
        # Similar a _create_executive_summary_sheet pero con datos de auditoría
        ws['A1'] = "REPORTE DE AUDITORÍA - RESUMEN EJECUTIVO"
        ws['A1'].font = Font(size=16, bold=True)
        ws.merge_cells('A1:F1')
        
        ws['A3'] = f"Generado: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        # Combinar estadísticas de incidencias y sobredescargo
        incident_stats = incident_reporter.get_summary_stats()
        overdischarge_stats = overdischarge_controller.get_statistics()
        
        row = 5
        ws[f'A{row}'] = "RESUMEN DE AUDITORÍA"
        ws[f'A{row}'].font = Font(bold=True)
        row += 1
        
        audit_summary = [
            ["Total registros procesados", incident_stats['total_records']],
            ["Tasa de éxito general", f"{incident_stats['success_rate']:.1f}%"],
            ["Total incidencias", incident_stats['total_incidents']],
            ["Incidencias críticas", incident_stats['critical_incidents']],
            ["Partidas con sobredescargo", overdischarge_stats['partidas_con_sobredescargo']],
            ["Estado general", "APROBADO" if incident_stats['critical_incidents'] == 0 else "REQUIERE ATENCIÓN"]
        ]
        
        for item, value in audit_summary:
            ws[f'A{row}'] = item
            ws[f'B{row}'] = value
            row += 1
    
    def _create_consistency_validation_sheet(self, wb: Workbook):
        """Crea la hoja de validaciones de consistencia"""
        ws = wb.create_sheet("Validaciones de Consistencia")
        
        df = consistency_validator.generate_consistency_report()
        
        if not df.empty:
            for r in dataframe_to_rows(df, index=False, header=True):
                ws.append(r)
            
            # Aplicar estilos
            for col in range(1, len(df.columns) + 1):
                cell = ws.cell(row=1, column=col)
                cell.fill = self.styles['header_warning']['fill']
                cell.font = self.styles['header_warning']['font']
        else:
            ws['A1'] = "✅ No se encontraron inconsistencias de datos"
            ws['A1'].fill = self.styles['cell_success']['fill']
    
    def _create_overdischarge_control_sheet(self, wb: Workbook):
        """Crea la hoja de control de sobredescargo"""
        ws = wb.create_sheet("Control de Sobredescargo")
        
        stats = overdischarge_controller.get_statistics()
        
        # Escribir estadísticas
        ws['A1'] = "CONTROL DE SOBREDESCARGO - ESTADÍSTICAS"
        ws['A1'].font = Font(bold=True, size=14)
        
        row = 3
        for key, value in stats.items():
            ws[f'A{row}'] = key.replace('_', ' ').title()
            ws[f'B{row}'] = value
            row += 1
    
    def _create_overdischarge_details_sheet(self, wb: Workbook):
        """Crea la hoja de detalles de sobredescargo"""
        ws = wb.create_sheet("Detalles de Sobredescargo")
        
        df = overdischarge_controller.get_overdischarge_details()
        
        if not df.empty:
            for r in dataframe_to_rows(df, index=False, header=True):
                ws.append(r)
            
            # Aplicar estilos críticos
            for col in range(1, len(df.columns) + 1):
                cell = ws.cell(row=1, column=col)
                cell.fill = self.styles['header_critical']['fill']
                cell.font = self.styles['header_critical']['font']
        else:
            ws['A1'] = "✅ No se detectaron sobredescargos"
            ws['A1'].fill = self.styles['cell_success']['fill']
    
    def _create_partida_summary_sheet(self, wb: Workbook):
        """Crea la hoja de resumen por partida"""
        ws = wb.create_sheet("Resumen por Partida")
        
        df = overdischarge_controller.get_partida_summary()
        
        if not df.empty:
            for r in dataframe_to_rows(df, index=False, header=True):
                ws.append(r)
            
            # Aplicar estilos
            for col in range(1, len(df.columns) + 1):
                cell = ws.cell(row=1, column=col)
                cell.fill = self.styles['header_info']['fill']
                cell.font = self.styles['header_info']['font']
        else:
            ws['A1'] = "No hay datos de partidas disponibles"

# Instancia global del generador
audit_report_generator = AuditReportGenerator()
